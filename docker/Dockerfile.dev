# Development container with Docker-in-Docker support
FROM mcr.microsoft.com/devcontainers/python:3.10-bullseye

# Install additional system dependencies
RUN apt-get update && export DEBIAN_FRONTEND=noninteractive \
    && apt-get -y install --no-install-recommends \
        # Development tools
        build-essential \
        curl \
        wget \
        git \
        vim \
        nano \
        htop \
        tree \
        jq \
        unzip \
        zip \
        # Network tools
        net-tools \
        iputils-ping \
        telnet \
        netcat \
        # Database clients
        postgresql-client \
        # Python development
        python3-dev \
        python3-pip \
        python3-venv \
        # Node.js will be installed separately with newer version
        # Docker CLI (will be replaced by Docker-in-Docker feature)
        docker.io \
        docker-compose \
        # Additional utilities
        zsh \
        fonts-powerline \
        fzf \
        mc \
    && apt-get autoremove -y && apt-get clean -y && rm -rf /var/lib/apt/lists/*

# Install Node.js 18 LTS from NodeSource
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs \
    && npm install -g npm@latest

# Install Python development tools
RUN pip3 install --upgrade pip \
    && pip3 install \
        ruff \
        mypy \
        pytest \
        pytest-asyncio \
        pytest-cov \
        pytest-mock \
        pytest-xdist \
        pytest-html \
        behave \
        behave-html-formatter \
        playwright \
        pytest-playwright \
        requests \
        responses \
        pre-commit \
        ipython \
        jupyter \
        types-requests \
        types-python-dateutil

# Install Node.js development tools
RUN npm install -g \
    typescript \
    @types/node \
    prettier \
    eslint \
    @typescript-eslint/parser \
    @typescript-eslint/eslint-plugin

# Install Playwright via npm and then install browsers (as root)
RUN npm install -g playwright \
    && npx playwright install --with-deps chromium

# Set up zsh as default shell for vscode user
RUN chsh -s /bin/zsh vscode

# Create workspace directory
RUN mkdir -p /workspace && chown vscode:vscode /workspace

# Switch to vscode user
USER vscode

# Set up zsh configuration (install oh-my-zsh if not already present)
RUN if [ ! -d "/home/<USER>/.oh-my-zsh" ]; then \
        sh -c "$(curl -fsSL https://raw.github.com/ohmyzsh/ohmyzsh/master/tools/install.sh)" "" --unattended; \
    fi \
    && echo 'export PATH=$PATH:/usr/local/bin' >> ~/.zshrc \
    && echo 'export PYTHONPATH=/workspace' >> ~/.zshrc \
    && echo 'alias ll="ls -la"' >> ~/.zshrc \
    && echo 'alias la="ls -la"' >> ~/.zshrc \
    && echo 'alias dc="docker-compose"' >> ~/.zshrc \
    && echo 'alias dcu="docker-compose up"' >> ~/.zshrc \
    && echo 'alias dcd="docker-compose down"' >> ~/.zshrc \
    && echo 'alias dcb="docker-compose build"' >> ~/.zshrc \
    && echo 'alias dps="docker ps"' >> ~/.zshrc \
    && echo 'alias di="docker images"' >> ~/.zshrc \
    && echo 'alias f="fzf"' >> ~/.zshrc \
    && echo 'alias fv="fzf --preview=\"cat {}\" --preview-window=right:60%"' >> ~/.zshrc \
    && echo 'alias mc="mc --nosubshell"' >> ~/.zshrc \
    && echo '# Enable fzf key bindings and fuzzy completion' >> ~/.zshrc \
    && echo 'source /usr/share/doc/fzf/examples/key-bindings.zsh 2>/dev/null || true' >> ~/.zshrc \
    && echo 'source /usr/share/doc/fzf/examples/completion.zsh 2>/dev/null || true' >> ~/.zshrc

# Set up git configuration template
RUN git config --global init.defaultBranch master \
    && git config --global pull.rebase false \
    && git config --global core.autocrlf input

# Create directories for development
RUN mkdir -p /home/<USER>/.local/bin \
    && mkdir -p /home/<USER>/.cache \
    && mkdir -p /home/<USER>/.config

# Set working directory
WORKDIR /workspace

# Set environment variables
ENV PYTHONPATH=/workspace
ENV PATH="/home/<USER>/.local/bin:$PATH"
ENV SHELL=/bin/zsh

# Expose common development ports
EXPOSE 8000 3000 5601 9200 5000

# Default command
CMD ["/bin/zsh"]
